import streamlit as st
import streamlit_highcharts as hct
import pandas as pd

def create_semi_circle_chart(picking_hours, backroom_hours, total_hours, height=300, budgettab=False ):
    """
    Creates a semi-circle chart showing picking efficiency.
    
    Args:
        picking_hours: Number of hours spent picking
        total_hours: Total number of hours
        height: Height of the chart in pixels
        budgettab: Whether chart is displayed in budget tab (affects styling)
        
    Returns:
        Renders the chart directly in Streamlit
    """
    # Create a copy of the config to avoid modifying the original
    semi_circle_config = {
        "chart": {
            "type": "pie",
            "backgroundColor": "transparent",
            "style": {
                "fontFamily": "'Segoe UI', Arial, sans-serif"
            },
            "height": height
        },
        "title": {
            "text": "<b>Picking/<br/>Backroom</b>" if not budgettab else "",
            "align": 'center',
            "verticalAlign": 'middle',
            "y": 90,  # Reduced from 40 to avoid overlap
            "style": {
                "fontSize": '14px',  # Reduced from 16px
                "color": '#ffffff'
            },
            "useHTML": True
        },
        "subtitle": {
            "text": 'Total Hours' if not budgettab else "<b>Picking/Backroom</b>",
            "align": 'left',
            "style": {
                "fontSize": '18px',
                "fontWeight": '600',
                "color": '#ffffff'
            }
        },
        "tooltip": {
            "pointFormat": '<b>{point.y:,.1f} hours</b>',
            "backgroundColor": 'rgba(39, 43, 48, 0.95)',
            "borderColor": '#555',
            "style": {
                "color": '#ffffff'
            }
        },
        "plotOptions": {
            "pie": {
                "dataLabels": {
                    "enabled": True,
                    "distance": -25,  # Reduced from -30
                    "format": '<b>{point.y:,.0f} h</b>',
                    "style": {
                        "fontWeight": 'bold',
                        "color": 'white',
                        "fontSize": '12px',  # Reduced from 14px
                        "textOutline": '2px rgba(0, 0, 0, 0.7)'
                    }
                },
                "startAngle": -90,
                "endAngle": 90,
                "center": ['50%', '75%' if budgettab else '85%'],  # Adjusted for budget tab
                "size": '160%' if budgettab else '100%',  # Increased size for budget tab
                "innerSize": '50%'  # Adjusted for better proportions
            }
        },
        "colors": ['#3498db', '#34495e'],
        "series": [{
            "type": 'pie',
            "name": 'Hours',
            "data": [
                {
                    "name": "Picking",
                    "y": picking_hours
                },
                {
                    "name": "Backroom",
                    "y": backroom_hours if budgettab else total_hours - picking_hours
                }
            ]
        }],
        "credits": {"enabled": False}
    }
    
    # Add spacing adjustment for budget tab to maximize chart area usage
    if budgettab:
        semi_circle_config["chart"]["spacingTop"] = 0
        semi_circle_config["chart"]["spacingBottom"] = 0
        semi_circle_config["chart"]["spacingLeft"] = 0
        semi_circle_config["chart"]["spacingRight"] = 0
        semi_circle_config["chart"]["marginTop"] = 20
        semi_circle_config["chart"]["marginBottom"] = -50
    
    return hct.streamlit_highcharts(semi_circle_config, height)



def create_activity_pie_chart(pie_data, height=400, legend_at_bottom=True, budgettab=False):
    """
    Creates a pie chart showing activity distribution.
    
    Args:
        pie_data: List of dictionaries with 'name' and 'y' keys
        height: Height of the chart in pixels
        legend_at_bottom: If True, places the legend at the bottom of the chart
        budgettab: If True, displays slice names instead of legend (for budget tab)
        
    Returns:
        Renders the chart directly in Streamlit
    """
    activity_chart_config = {
        "chart": {
            "type": "pie",
            "backgroundColor": "transparent",
            "style": {
                "fontFamily": "'Segoe UI', Arial, sans-serif"
            },
            "height": height
        },
        "title": {
            "text": 'Backroom Activities (Hours)',
            "align": 'left',
            "style": {
                "fontSize": '18px',
                "fontWeight": '600',
                "color": '#ffffff'
            }
        },
        "tooltip": {
            "pointFormat": '<b>{point.y:,.0f} hours</b> ({point.percentage:.1f}%)',
            "backgroundColor": 'rgba(39, 43, 48, 0.95)',
            "borderColor": '#555',
            "style": {
                "color": '#ffffff'
            }
        },
        "plotOptions": {
            "pie": {
                "allowPointSelect": True,
                "cursor": 'pointer',
                "showInLegend": not budgettab,  # Hide legend on budget tab
                "slicedOffset": 10,
                "states": {
                    "hover": {
                        "brightness": 0.1,
                        "halo": {
                            "size": 10,
                            "opacity": 0.5
                        }
                    }
                },
                "dataLabels": {
                    "enabled": True,
                    "format": '<b>{point.name}</b><br/>{point.y:,.0f} h' if budgettab else '<b>{point.y:,.0f} h</b>',
                    "distance": 15 if budgettab else -40,  # Outside labels for budget tab
                    "style": {
                        "fontSize": '11px' if budgettab else '12px',
                        "color": '#ffffff',
                        "textOutline": '2px rgba(0, 0, 0, 0.8)',
                        "fontWeight": 'bold'
                    },
                    "connectorColor": '#ffffff' if budgettab else 'transparent',  # Show connectors for budget tab
                    "connectorWidth": 1 if budgettab else 0
                },
                "innerSize": '40%'
            }
        },
        "legend": {
            "enabled": not budgettab,  # Disable legend for budget tab
            "layout": "vertical" if not budgettab and legend_at_bottom else "vertical",
            "align": "right" if not budgettab and legend_at_bottom else "right",
            "verticalAlign": "middle" if not budgettab and legend_at_bottom else "middle",
            "itemStyle": {
                "color": "#fff",
                "fontWeight": "normal"
            },
            "backgroundColor": "transparent",
            "borderWidth": 0,
            "shadow": False,
            "padding": 10,
            "itemMarginTop": 5,
            "symbolRadius": 4,
            "symbolWidth": 12,
            "symbolHeight": 12
        },
        "colors": ['#3b82f6', '#10b981', '#ef4444', '#f59e0b', '#8b5cf6', '#06b6d4', '#f97316',  '#84cc16'],
        "series": [{
            "name": 'Hours',
            "data": pie_data
        }],
        "credits": {"enabled": False}
    }
    
    return hct.streamlit_highcharts(activity_chart_config, height)

def create_enhanced_bar_chart(data, y_column, title, y_title, color='#3b82f6', highlight_color='#ef4444', selected_store_num=None, chart_key=None, format_thousands=False):
    """
    Creates an enhanced bar chart for store performance metrics.

    Args:
        data: DataFrame containing the data
        y_column: Column name for the y-axis values
        title: Chart title
        y_title: Y-axis title
        color: Default bar color
        highlight_color: Color for the selected store
        selected_store_num: Store number to highlight
        chart_key: Unique key for the chart
        format_thousands: Whether to format values in thousands (K format)

    Returns:
        Chart configuration dictionary
    """
    categories = [f"{int(row['Store'])}" for _, row in data.iterrows()]
    store_names = [row['Name'] for _, row in data.iterrows()]
    values = data[y_column].round(2).tolist()
    
    data_points = []
    for idx, (y, store_num, store_name) in enumerate(zip(values, data['Store'], store_names)):
        is_selected = str(store_num) == str(selected_store_num)
        point = {
            'y': y,
            'color': highlight_color if is_selected else color,
            'storeName': store_name,
            'storeNumber': int(store_num)
        }
        data_points.append(point)
    
    return {
        "chart": {
            "type": "column",
            "height": 350,
            "backgroundColor": "transparent",
            "style": {
                "fontFamily": "'Segoe UI', Arial, sans-serif"
            }
        },
        "title": {
            "text": title,
            "style": {
                "fontSize": '16px',
                "fontWeight": 'bold',
                "color": '#ffffff'
            }
        },
        "xAxis": {
            "categories": categories,
            "labels": {
                "rotation": -45,
                "style": {
                    "fontSize": '11px',
                    "color": '#b4b4b4',
                    "cursor": 'default'
                }
            },
            "title": {
                "text": 'Store Number',
                "style": {
                    "color": '#b4b4b4'
                }
            },
            "lineColor": '#555',
            "tickColor": '#555'
        },
        "yAxis": {
            "title": {
                "text": y_title,
                "style": {
                    "color": '#b4b4b4'
                }
            },
            "min": 0,
            "gridLineColor": '#333',
            "labels": {
                "style": {
                    "color": '#b4b4b4'
                }
            }
        },
        "legend": {"enabled": False},
        "tooltip": {
            "headerFormat": '<b>Store {point.storeNumber}</b><br/>',
            "pointFormat": '{point.storeName}<br/>{series.name}: <b>{point.y:,.1f}</b>',
            "backgroundColor": 'rgba(39, 43, 48, 0.95)',
            "borderColor": '#555',
            "style": {
                "color": '#ffffff'
            }
        },
        "plotOptions": {
            "column": {
                "borderRadius": 8,
                "borderWidth": 0,
                "cursor": 'default',
                "states": {
                    "hover": {
                        "brightness": 0.2,
                        "borderColor": '#ffffff',
                        "borderWidth": 2
                    }
                },
                "dataLabels": {
                    "enabled": True,
                    "format": '{point.y:,.1f}',
                    "style": {
                        "fontSize": '10px',
                        "color": '#ffffff',
                        "textOutline": '1px rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        },
        "series": [{
            "name": y_title,
            "data": data_points,
            "allowPointSelect": False
        }],
        "credits": {"enabled": False}
    }


def create_hours_utilization_chart(data, selected_store_num=None, height=400, chart_key=None, clickable=True):
    """
    Creates a stacked bar chart showing picking vs Backroom hours utilization with click functionality.
    """
    categories = []
    picking_data = []
    non_picking_data = []
    store_info = []  # Store complete info for click handling
    
    # Define colors
    picking_color = '#10b981'  # Green for picking
    non_picking_color = '#475569'  # Dark gray for Backroom
    selected_color = '#ef4444'  # Red for selected store
    
    # Sort by total hours descending
    sorted_data = data.sort_values('Total hours', ascending=False)
    
    for _, store in sorted_data.iterrows():
        store_num = str(int(store['Store']))
        store_name = store['Name']
        categories.append(store_num)
        picking_hours = store['Picking hours']
        non_picking_hours = store['Total hours'] - picking_hours
        
        is_selected = store_num == str(selected_store_num)
        
        store_info.append({
            'storeNumber': int(store_num),
            'storeName': store_name
        })
        
        picking_data.append({
            'y': picking_hours,
            'color': selected_color if is_selected else picking_color,
            'storeNumber': int(store_num),
            'storeName': store_name
        })
        non_picking_data.append({
            'y': non_picking_hours,
            'color': selected_color if is_selected else non_picking_color,
            'borderWidth': 2 if is_selected else 0,
            'borderColor': '#ffffff',
            'storeNumber': int(store_num),
            'storeName': store_name
        })
    
    click_function = f"""function() {{
        const event = new CustomEvent('streamlit:setComponentValue', {{
            detail: {{
                key: '{chart_key}_click',
                value: {{
                    storeNumber: this.storeNumber,
                    storeName: this.storeName
                }}
            }}
        }});
        window.parent.document.dispatchEvent(event);
    }}""" if clickable else ""
    
    return {
        "chart": {
            "type": "column",
            "height": height,
            "backgroundColor": "transparent"
        },
        "title": {
            "text": "Hours Utilization: Picking vs Backroom",
            "style": {
                "fontSize": '16px',
                "fontWeight": 'bold',
                "color": '#ffffff'
            }
        },
        "xAxis": {
            "categories": categories,
            "title": {
                "text": 'Store Number',
                "style": {"color": '#b4b4b4'}
            },
            "labels": {
                "style": {"color": '#b4b4b4', "cursor": 'pointer' if clickable else 'default'},
                "rotation": -45
            }
        },
        "yAxis": {
            "min": 0,
            "title": {
                "text": 'Hours',
                "style": {"color": '#b4b4b4'}
            },
            "labels": {
                "style": {"color": '#b4b4b4'}
            },
            "gridLineColor": '#333'
        },
        "legend": {
            "align": 'center',
            "verticalAlign": 'bottom',
            "itemStyle": {
                "color": '#ffffff',
                "fontWeight": 'normal',
                "fontSize": '12px'
            },
            "itemHoverStyle": {
                "color": '#ffffff'
            },
            "itemHiddenStyle": {
                "color": '#666666'
            }
        },
        "tooltip": {
            "headerFormat": '<b>Store {point.key}</b><br/>',
            "pointFormat": '{series.name}: <b>{point.y:,.1f} hours</b>',
            "backgroundColor": 'rgba(39, 43, 48, 0.95)',
            "borderColor": '#555',
            "style": {"color": '#ffffff'},
            "shared": True
        },
        "plotOptions": {
            "column": {
                "stacking": 'normal',
                "borderRadius": 8,
                "cursor": 'pointer' if clickable else 'default',
                "dataLabels": {
                    "enabled": False
                },
                "groupPadding": 0.1,
                "pointPadding": 0.05,
                "states": {
                    "hover": {
                        "brightness": 0.1
                    }
                },
                "events": {} if not clickable else {
                    "click": click_function
                }
            }
        },
        "series": [{
            "name": 'Picking Hours',
            "data": picking_data,
            "color": picking_color
        }, {
            "name": 'Backroom Hours',
            "data": non_picking_data,
            "color": non_picking_color
        }],
        "credits": {"enabled": False}
    }



def render_store_performance_charts(country_stores, selected_store):
    """
    Renders the store performance charts in a 2x2 grid with click functionality.
    
    Args:
        country_stores: DataFrame with store data for the selected country
        selected_store: Selected store string in format "StoreNumber StoreName"
    """
    # Get selected store number
    selected_store_num = None
    if selected_store and selected_store != 'All Stores':
        selected_store_num = selected_store.split(' ')[0]

    
    # Create metric charts in a 3x2 grid
    col1, col2 = st.columns(2)

    with col1:
        # Pickrate Target
        with st.container(key="store_chart_1"):
            sorted_data = country_stores.sort_values('Model pickrate', ascending=False)
            chart = create_enhanced_bar_chart(
                sorted_data, 'Model pickrate', 'Model Pickrate (items/hour)',
                'Items per Hour', '#3498db', '#e74c3c', selected_store_num,
                chart_key='pickrate_chart'
            )
            hct.streamlit_highcharts(chart, key="pickrate_chart")

        # Items per Order
        with st.container(key="store_chart_2"):
            sorted_data = country_stores.sort_values('Item/order (Basket size)', ascending=False)
            chart = create_enhanced_bar_chart(
                sorted_data, 'Item/order (Basket size)', 'Items per Order (Basket Size)',
                'Items per Order', '#2ecc71', '#e74c3c', selected_store_num,
                chart_key='items_order_chart'
            )
            hct.streamlit_highcharts(chart, key="items_order_chart")

        # Items per TPN
        with st.container(key="store_chart_5"):
            sorted_data = country_stores.sort_values('Item/TPN', ascending=False)
            chart = create_enhanced_bar_chart(
                sorted_data, 'Item/TPN', 'Items per TPN',
                'Items per TPN', '#e67e22', '#e74c3c', selected_store_num,
                chart_key='items_tpn_chart'
            )
            hct.streamlit_highcharts(chart, key="items_tpn_chart")

        # New Picked Items chart (moved to left column)
        with st.container(key="store_chart_picked_items"):
            sorted_data = country_stores.sort_values('Picked item #', ascending=False)
            chart = create_enhanced_bar_chart(
                sorted_data, 'Picked item #', 'Picked Items',
                'Picked Items', '#17a2b8', '#e74c3c', selected_store_num,
                chart_key='picked_items_chart', format_thousands=True
            )
            hct.streamlit_highcharts(chart, key="picked_items_chart")

    with col2:
        # Items per Tray
        with st.container(key="store_chart_3"):
            sorted_data = country_stores.sort_values('Item/tray', ascending=False)
            chart = create_enhanced_bar_chart(
                sorted_data, 'Item/tray', 'Items per Tray',
                'Items per Tray', '#9b59b6', '#e74c3c', selected_store_num,
                chart_key='items_tray_chart'
            )
            hct.streamlit_highcharts(chart, key="items_tray_chart")

        # Trays per Order
        with st.container(key="store_chart_4"):
            sorted_data = country_stores.sort_values('Tray/order', ascending=True)
            chart = create_enhanced_bar_chart(
                sorted_data, 'Tray/order', 'Trays per Order',
                'Trays per Order', '#f39c12', '#e74c3c', selected_store_num,
                chart_key='tray_order_chart'
            )
            hct.streamlit_highcharts(chart, key="tray_order_chart")

        # Hours Utilization chart
        with st.container(key="store_chart_6"):
            sorted_data = country_stores.sort_values('Total hours', ascending=False)
            chart = create_hours_utilization_chart(
                sorted_data, selected_store_num, chart_key='hours_utilization_chart'
            )
            hct.streamlit_highcharts(chart, key="hours_utilization_chart")




    
def create_dotcom_sales_chart(budget_df):
    """
    Combination chart with stacked bars (Sales + Units) and line (Hours)
    Similar to the reference image with blue sales, yellow units, purple hours line
    Includes currency conversion to GBP when "All Countries" is selected
    """
    # Get current period and week dynamically
    from budget import get_period_week_info
    current_period, current_week = get_period_week_info()
    
    # Basic validation
    required_columns = ['Dotcom sales £', 'Dotcom Units', 'FIN WORKED']
    missing_columns = [col for col in required_columns if col not in budget_df.columns]
    
    if budget_df.empty or missing_columns:
        st.error(f"Missing required columns: {', '.join(missing_columns)}")
        return
    
    # Apply filters
    filtered_df = budget_df.copy()
    
    # Country filter
    country_display = "All Countries"
    convert_to_gbp = False  # Flag to determine if we need currency conversion
    
    if (hasattr(st.session_state, 'selected_country') and 
        st.session_state.selected_country != 'All Countries' and
        'COUNTRY' in filtered_df.columns):
        filtered_df = filtered_df[filtered_df['COUNTRY'] == st.session_state.selected_country]
        country_display = st.session_state.selected_country
    else:
        # When "All Countries" is selected, we need to convert to GBP
        convert_to_gbp = True

    # Currency symbol logic based on country
    currency_symbol = "£"  # Default to GBP
    if hasattr(st.session_state, 'selected_country') and not convert_to_gbp:
        if st.session_state.selected_country == 'HU':
            currency_symbol = "Ft"
        elif st.session_state.selected_country == 'CZ':
            currency_symbol = "Kč"
        elif st.session_state.selected_country == 'SK':
            currency_symbol = "€"
    
    # Store filter
    store_display = ""
    if (hasattr(st.session_state, 'selected_store') and 
        hasattr(st.session_state, 'show_store_filter') and
        st.session_state.show_store_filter and 
        st.session_state.selected_store != 'All Stores' and
        'SHOP' in filtered_df.columns):
        store_number = st.session_state.selected_store.split(' ')[0]
        filtered_df = filtered_df[filtered_df['SHOP'].astype(str) == store_number]
        store_display = f" - {st.session_state.selected_store}"
    
    if filtered_df.empty:
        st.warning("No data after filtering")
        return
    
    # Currency conversion function
    def convert_sales_to_gbp(df):
        """Convert sales values to GBP based on country"""
        if not convert_to_gbp or 'COUNTRY' not in df.columns:
            return df['Dotcom sales £']
        
        # Exchange rates
        exchange_rates = {
            'CZ': 29.82,
            'SK': 1.186,
            'HU': 473.2
        }
        
        converted_sales = df['Dotcom sales £'].copy()
        
        for country, rate in exchange_rates.items():
            mask = df['COUNTRY'] == country
            converted_sales.loc[mask] = converted_sales.loc[mask] / rate
            
        return converted_sales
    
    # Apply currency conversion if needed
    if convert_to_gbp:
        filtered_df = filtered_df.copy()
        filtered_df['Dotcom sales £'] = convert_sales_to_gbp(filtered_df)
    
    # Process data
    if 'PERIOD' in filtered_df.columns and 'WEEK' in filtered_df.columns:
        # Get selection info FIRST
        selected_period = st.session_state.get('selected_period', 1)
        selected_week = st.session_state.get('selected_week', 1)
        
        # Check if we should show only selected period or full year
        period_only_view = st.session_state.get('period_only_view', False)
        
        if period_only_view:
            # Filter to show only the selected period
            period_data = filtered_df[filtered_df['PERIOD'] == selected_period]
            if period_data.empty:
                st.warning(f"No data available for Period {selected_period}")
                return
            aggregated_data = period_data.groupby(['PERIOD', 'WEEK']).agg({
                'Dotcom sales £': 'sum',
                'Dotcom Units': 'sum',
                'FIN WORKED': 'sum'
            }).reset_index()
        else:
            # Show full year data
            aggregated_data = filtered_df.groupby(['PERIOD', 'WEEK']).agg({
                'Dotcom sales £': 'sum',
                'Dotcom Units': 'sum',
                'FIN WORKED': 'sum'
            }).reset_index()
        
        aggregated_data = aggregated_data.sort_values(['PERIOD', 'WEEK'])
        
        if aggregated_data.empty:
            st.warning("No grouped data available")
            return
        
        # Find the first week of the selected period
        first_week_of_period = None
        for _, row in aggregated_data.iterrows():
            if int(row['PERIOD']) == selected_period:
                if first_week_of_period is None or int(row['WEEK']) < first_week_of_period:
                    first_week_of_period = int(row['WEEK'])
        
        # Store chart-specific period tracking without affecting session state
        chart_previous_period_key = 'dotcom_chart_previous_period'
        
        # If period changed, highlight the first week of that period in the chart
        if hasattr(st.session_state, chart_previous_period_key) and st.session_state[chart_previous_period_key] != selected_period:
            selected_week_for_highlight = first_week_of_period if first_week_of_period is not None else selected_week
        else:
            selected_week_for_highlight = selected_week
        
        # Store current period for next comparison using chart-specific key
        st.session_state[chart_previous_period_key] = selected_period
        
        # Create data for stacked bars and line
        labels = []
        sales_data = []      # Blue bars (bottom stack)
        units_data = []      # Yellow bars (top stack)
        hours_line_data = [] # Purple line
        
        # Scale factors to make the data comparable in the same chart
        # We'll scale sales to be similar magnitude to units for better visualization
        max_sales = aggregated_data['Dotcom sales £'].max()
        max_units = aggregated_data['Dotcom Units'].max()
        max_hours = aggregated_data['FIN WORKED'].max()
        
        # Scale sales to be comparable to units for stacking
        sales_scale_factor = max_units / max_sales if max_sales > 0 else 1
        
        # Scale hours to be visually above the bars (higher than max stacked value)
        max_stacked = max_units + (max_sales * sales_scale_factor)
        hours_scale_factor = max_stacked * 1.5 / max_hours if max_hours > 0 else 1
        hours_baseline = max_stacked * 0.2  # Start hours line above the bars
        
        for _, row in aggregated_data.iterrows():
            period = int(row['PERIOD'])
            week = int(row['WEEK'])
            
            # Scale sales to be comparable to units for stacking
            sales_value = float(row['Dotcom sales £']) * sales_scale_factor
            units_value = float(row['Dotcom Units'])
            hours_value = float(row['FIN WORKED'])
            
            label = f"P{period}W{week}"
            labels.append(label)
            
            # Determine week type for styling
            is_selected = (period == selected_period and week == selected_week_for_highlight)
            is_current = (week == current_week)
            is_historical = (week < current_week)
            is_future = (week > current_week)
            
            # Common point properties
            base_point = {
                "period": period,
                "week": week,
                "name": f"Period {period}, Week {week}",
                "originalSales": float(row['Dotcom sales £'])  # Keep original for tooltip
            }
            
            # Sales bar data (bottom of stack) - Blue with different shades based on week type
            if is_selected:
                sales_color = '#ef4444'  # Red for selected
            elif is_current:
                sales_color = '#06b6d4'  # Cyan for current
            elif is_historical:
                sales_color = '#64748b'  # Muted gray-blue for historical
            else:  # is_future
                sales_color = '#1e40af'  # Standard blue for future
                
            sales_data.append({
                **base_point,
                "y": sales_value,
                "color": sales_color,
                "borderWidth": 3 if is_selected else (2 if is_current else 0),
                "borderColor": '#ffffff',
                "opacity": 0.7 if is_historical else 1.0,  # Fade historical weeks
                "custom": {
                    "displayValue": f"{currency_symbol} {float(row['Dotcom sales £']):,.0f}"
                }
            })
            
            # Units bar data (top of stack) - Yellow/Orange with different shades
            if is_selected:
                units_color = '#ef4444'  # Red for selected
            elif is_current:
                units_color = '#06b6d4'  # Cyan for current
            elif is_historical:
                units_color = '#a3a3a3'  # Muted gray for historical
            else:  # is_future
                units_color = '#f59e0b'  # Standard orange for future
                
            units_data.append({
                **base_point,
                "y": units_value,
                "color": units_color,
                "borderWidth": 3 if is_selected else (2 if is_current else 0),
                "borderColor": '#ffffff',
                "opacity": 0.7 if is_historical else 1.0,  # Fade historical weeks
                "custom": {
                    "displayValue": f"{units_value:,.0f}"
                }
            })
            
            # Hours line data - Purple (scaled to be above bars)
            scaled_hours_value = (hours_value * hours_scale_factor) + hours_baseline
            
            if is_selected:
                line_color = '#ef4444'  # Red for selected
                marker_size = 8
                line_width = 4
            elif is_current:
                line_color = '#06b6d4'  # Cyan for current
                marker_size = 6
                line_width = 3
            elif is_historical:
                line_color = '#9ca3af'  # Muted gray for historical
                marker_size = 4
                line_width = 2
            else:  # is_future
                line_color = '#8b5cf6'  # Standard purple for future
                marker_size = 5
                line_width = 3
                
            hours_line_data.append({
                **base_point,
                "y": scaled_hours_value,
                "originalHours": hours_value,
                "marker": {
                    "fillColor": line_color,
                    "lineColor": '#ffffff' if (is_selected or is_current) else line_color,
                    "lineWidth": 3 if (is_selected or is_current) else 1,
                    "radius": marker_size
                },
                "opacity": 0.6 if is_historical else 1.0,  # Fade historical weeks
                "custom": {
                    "displayValue": f"{hours_value:,.0f}h"
                }
            })
        
        total_sales = aggregated_data['Dotcom sales £'].sum()
        total_units = aggregated_data['Dotcom Units'].sum()
        total_hours = aggregated_data['FIN WORKED'].sum()
        
    else:
        # Simple fallback
        total_sales = filtered_df['Dotcom sales £'].sum()
        total_units = filtered_df['Dotcom Units'].sum()
        total_hours = filtered_df['FIN WORKED'].sum()
        labels = ['Total']
        sales_data = [{"y": float(total_sales), "originalSales": float(total_sales), "custom": {"displayValue": f"{currency_symbol} {total_sales:,.0f}"}}]
        units_data = [{"y": float(total_units), "custom": {"displayValue": f"{total_units:,.0f}"}}]
        hours_line_data = [{"y": float(total_hours), "originalHours": float(total_hours), "custom": {"displayValue": f"{total_hours:,.0f}h"}}]
        sales_scale_factor = 1
        hours_scale_factor = 1
        hours_baseline = 0
    
    # Build title with filter information
    period_view_text = f" (Period {selected_period} Only)" if st.session_state.get('period_only_view', False) else ""
    title_text = f"Dotcom Performance: {country_display}{store_display}{period_view_text}"
    
    # Update subtitle to show conversion note when applicable
    conversion_note = " (Converted to GBP)" if convert_to_gbp else ""
    subtitle_text = f"{currency_symbol} {total_sales:,.0f} Budget Sales{conversion_note} | {total_units:,.0f} Units | {total_hours:,.0f} Worked Hours"
    
    # Chart config with stacked bars + line combination
    config = {
        "chart": {
            "type": "column",
            "height": 450,
            "backgroundColor": "transparent",
            "style": {"fontFamily": "'Inter', sans-serif"},
            "animation": {
                "duration": 1000,
                "easing": "easeOutQuart"
            }
        },
        "title": {
            "text": title_text,
            "style": {"color": "#fff", "fontWeight": "bold", "fontSize": "20px"}
        },
        "subtitle": {
            "text": subtitle_text,
            "style": {"color": "#cbd5e1", "fontSize": "16px"}
        },
        "xAxis": {
            "categories": labels,
            "labels": {
                "style": {"color": "#cbd5e1", "fontSize": "12px"},
                "rotation": -45 if len(labels) > 10 else 0
            },
            "lineColor": "#475569",
            "tickColor": "#475569",
            "crosshair": {
                "width": 1,
                "color": "rgba(255, 255, 255, 0.3)",
                "dashStyle": "shortdot"
            }
        },
        # "yAxis": [
        #     {
        #         # Primary Y-axis for all data (bars and scaled line)
        #         "id": "primary",
        #         "title": {
        #             "text": "Sales & Units (Hours line scaled above)",
        #             "style": {"color": "#cbd5e1"}
        #         },
        #         "labels": {
        #             "style": {"color": "#cbd5e1"},
        #             "format": "{value:,.0f}"
        #         },
        #         "gridLineColor": "#334155",
        #         "gridLineDashStyle": "shortdot",
        #         "opposite": False
        #     }
        # ],


        "yAxis": [
                {
                    # Primary Y-axis for all data (bars and scaled line)
                    "id": "primary",
                    "labels": {
                        "enabled": False
                    },
                    "gridLineColor": "#334155",
                    "gridLineDashStyle": "shortdot",
                    "opposite": False
                }
            ],
        "tooltip": {
            "enabled": True,
            "backgroundColor": "rgba(15, 23, 42, 0.95)",
            "borderColor": "#475569",
            "borderRadius": 12,
            "borderWidth": 1,
            "style": {"color": "#fff", "fontSize": "13px"},
            "useHTML": True,
            "shared": True,
            "headerFormat": '<div style="font-size:14px; font-weight:bold; padding:8px; text-align:center; border-bottom: 1px solid #475569; margin-bottom: 8px;">{point.key}</div>',
            "pointFormat": '<div style="padding:3px 12px; display: flex; justify-content: space-between; align-items: center;">' +
                         '<span style="color:{series.color}; font-weight:600;">{series.name}:</span>' +
                         '<span style="color:{series.color}; font-weight:700; margin-left: 10px;">{point.custom.displayValue}</span>' +
                         '</div>',
            "shadow": True
        },
        "plotOptions": {
            "column": {
                "stacking": "normal",
                "borderRadius": 4,
                "animation": {
                    "duration": 1000
                },
                "groupPadding": 0.1,
                "pointPadding": 0.05,
                "states": {
                    "hover": {
                        "brightness": 0.1
                    }
                }
            },
            "line": {
                "lineWidth": 3,
                "animation": {
                    "duration": 1200
                },
                "marker": {
                    "enabled": True,
                    "radius": 4
                },
                "states": {
                    "hover": {
                        "lineWidthPlus": 1
                    }
                }
            }
        },
        "series": [
            {
                "name": "Budget Sales",
                "type": "column",
                "data": sales_data,
                "color": '#1e40af',
                "yAxis": 0,
                "stacking": "normal",
                "showInLegend": True
            },
            {
                "name": "Units",
                "type": "column", 
                "data": units_data,
                "color": '#f59e0b',
                "yAxis": 0,
                "stacking": "normal",
                "showInLegend": True
            },
            {
                "name": "Worked Hours",
                "type": "line",
                "data": hours_line_data,
                "color": '#8b5cf6',
                "yAxis": 0,
                "lineWidth": 4,
                "marker": {
                    "enabled": True,
                    "radius": 5,
                    "fillColor": '#8b5cf6'
                },
                "showInLegend": True,
                "zIndex": 10
            },
            # Add invisible series for week status legend
            {
                "name": "Selected Week",
                "type": "column",
                "data": [],
                "color": '#ef4444',
                "showInLegend": True,
                "visible": True
            },
            {
                "name": "Current Week",
                "type": "column", 
                "data": [],
                "color": '#06b6d4',
                "showInLegend": True,
                "visible": True
            },
            {
                "name": "Historical Week(s)",
                "type": "column",
                "data": [],
                "color": '#64748b',
                "showInLegend": True,
                "visible": True
            }
        ],
        "legend": {
            "enabled": True,
            "align": "center",
            "verticalAlign": "top",
            "y": 25,
            "itemStyle": {
                "color": "#ffffff",
                "fontSize": "13px",
                "fontWeight": "600"
            },
            "itemHoverStyle": {
                "color": "#ffffff"
            },
            "symbolRadius": 4,
            "symbolWidth": 14,
            "symbolHeight": 14,
            "itemDistance": 20
        },
        "credits": {"enabled": False},
        "responsive": {
            "rules": [{
                "condition": {"maxWidth": 600},
                "chartOptions": {
                    "chart": {"height": 350},
                    "title": {"style": {"fontSize": "16px"}},
                    "subtitle": {"style": {"fontSize": "14px"}},
                    "legend": {"itemDistance": 15}
                }
            }]
        }
    }
    
    # Display chart
    try:
        hct.streamlit_highcharts(config, height=450)
        
    except Exception as e:
        st.error(f"Chart render failed: {e}")
        # Show raw data as fallback
        fallback_df = pd.DataFrame({
            'Sales': [total_sales],
            'Units': [total_units], 
            'Hours': [total_hours]
        }, index=['Total'])
        st.dataframe(fallback_df)







def create_total_hours_chart(budget_df):
    """
    Creates a modern, clean donut chart showing hours breakdown with enhanced styling and metrics.
    Features a prominent donut chart with clear percentages and a complementary metrics section.
    """
    # Basic validation
    required_columns = ['FIN WORKED', 'Holiday', 'Fit Day', 'Sickness', 'Absence']
    missing_columns = [col for col in required_columns if col not in budget_df.columns]
    
    if budget_df.empty or missing_columns:
        if missing_columns:
            st.error(f"Missing required columns: {', '.join(missing_columns)}")
        else:
            st.error("No valid data available for hours breakdown")
        return
    
    # Apply filters
    filtered_df = budget_df.copy()
    
    # Country filter
    country_display = "All Countries"
    if (hasattr(st.session_state, 'selected_country') and 
        st.session_state.selected_country != 'All Countries' and
        'COUNTRY' in filtered_df.columns):
        filtered_df = filtered_df[filtered_df['COUNTRY'] == st.session_state.selected_country]
        country_display = st.session_state.selected_country
    
    # Store filter
    store_display = ""
    if (hasattr(st.session_state, 'selected_store') and 
        hasattr(st.session_state, 'show_store_filter') and
        st.session_state.show_store_filter and 
        st.session_state.selected_store != 'All Stores' and
        'SHOP' in filtered_df.columns):
        store_number = st.session_state.selected_store.split(' ')[0]
        filtered_df = filtered_df[filtered_df['SHOP'].astype(str) == store_number]
        store_display = f" - {st.session_state.selected_store}"
    
    # Period and Week filter
    period_week_filter = ""
    if (hasattr(st.session_state, 'selected_period') and 
        hasattr(st.session_state, 'selected_week') and
        'PERIOD' in filtered_df.columns and
        'WEEK' in filtered_df.columns):
        selected_period = st.session_state.get('selected_period')
        selected_week = st.session_state.get('selected_week')
        
        if selected_period and selected_week:
            filtered_df = filtered_df[
                (filtered_df['PERIOD'] == selected_period) & 
                (filtered_df['WEEK'] == selected_week)
            ]
            period_week_filter = f" - P{selected_period}W{selected_week}"
    
    if filtered_df.empty:
        st.warning("No data after filtering for hours breakdown")
        return
    
    # Process data - aggregate hours by category
    hours_data = {}
    for category in required_columns:
        if category in filtered_df.columns:
            hours_data[category] = filtered_df[category].sum()
    
    # Remove categories with zero hours for cleaner visualization
    hours_data = {k: v for k, v in hours_data.items() if v > 0}
    
    # Calculate total hours
    total_hours = sum(hours_data.values())
    
    if total_hours == 0:
        st.warning("No hours data available for the selected filters")
        return
    
    # Handle case where only one category has hours
    single_category = len(hours_data) == 1
    
    # If only one category exists, add a dummy category for visual purposes
    if single_category:
        # Get the existing category
        existing_category = list(hours_data.keys())[0]
        existing_value = hours_data[existing_category]
        
        # Add a tiny dummy segment that won't be visible but allows the chart to render
        dummy_category = "Other"
        hours_data[dummy_category] = 0.001  # Tiny value that won't affect calculations
    
    # Define modern color palette with better contrast
    category_colors = {
        'FIN WORKED': '#3b82f6',      # Professional Blue
        'Holiday': '#10b981',         # Fresh Green  
        'Fit Day': '#8b5cf6',         # Modern Purple
        'Sickness': '#f59e0b',        # Warm Amber
        'Absence': '#ef4444',         # Alert Red
        'Other': '#6b7280'            # Gray for dummy category
    }
    
    # Prepare data for donut chart, sorted by value descending
    sorted_categories = sorted(hours_data.keys(), key=lambda x: hours_data[x], reverse=True)
    
    pie_data = []
    for category in sorted_categories:
        value = hours_data[category]
        # For the dummy category, use a tiny percentage that won't be visible
        percentage = 0.001 if category == "Other" and single_category else (value / total_hours) * 100
        display_name = 'Worked Hours' if category == 'FIN WORKED' else category
        
        # Skip adding the dummy category to the visible data
        if category == "Other" and single_category:
            continue
            
        pie_data.append({
            "name": display_name,
            "original_name": category,  # Keep original for calculations
            "y": value,
            "percentage": percentage,
            "color": category_colors.get(category, '#6b7280')
        })
    
    # Build title with filter information
    title_text = f"Hours Breakdown: {country_display}{store_display}{period_week_filter}"
    
    # Create layout with chart and metrics side by side
    col1, col2 = st.columns([3,0.9])
    
    with col1:
        # Enhanced donut chart config
        config = {
            "chart": {
                "type": "pie",
                "height": 450,
                "backgroundColor": "transparent",
                "style": {"fontFamily": "'Inter', sans-serif"},
                "animation": {"duration": 1200},
                "spacingTop": 0,
                "spacingBottom": 0,
                "spacingLeft": 0,
                "spacingRight": 0,
                "marginTop": 20,
                "marginBottom": -20
            },
            "title": {
                "text": title_text,
                "style": {
                    "color": "#fff", 
                    "fontWeight": "bold", 
                    "fontSize": "18px",
                    "marginBottom": "20px"
                }
            },
            "subtitle": {
                "text": f"<div style='text-align: center; margin-top: 10px;'><span style='font-size: 28px; font-weight: 700; color: #3b82f6;'>{total_hours:,.0f}</span><br/><span style='font-size: 14px; color: #cbd5e1;'>Total Hours</span></div>",
                "useHTML": True,
                "align": "center",
                "verticalAlign": "middle",
                "style": {"color": "#cbd5e1"}
            },
            "tooltip": {
                "enabled": True,
                "backgroundColor": "rgba(15, 23, 42, 0.95)",
                "borderColor": "#475569",
                "borderRadius": 12,
                "borderWidth": 1,
                "style": {"color": "#fff", "fontSize": "13px"},
                "useHTML": True,
                "pointFormat": '<div style="padding: 8px; text-align: center;"><div style="font-size: 16px; font-weight: 700; margin: 4px 0;">{point.y:,.0f} hours</div><div style="color: #94a3b8; font-size: 12px;">{point.percentage:.1f}% of total</div></div>',
                "shadow": True
            },
            "plotOptions": {
                "pie": {
                    "allowPointSelect": True,
                    "cursor": "pointer",
                    "innerSize": "65%",  # Creates the donut hole
                    "size": "85%",
                    "center": ["50%", "50%"],
                    "startAngle": -90,
                    "dataLabels": {
                        "enabled": True,
                        "distance": 25,
                        "style": {
                            "fontSize": "12px",
                            "fontWeight": "600",
                            "color": "#ffffff",
                            "textOutline": "2px rgba(0, 0, 0, 0.8)"
                        },
                        "format": '<span style="font-size: 11px;">{point.name}</span><br/><span style="font-size: 14px; font-weight: 700;">{point.percentage:.1f}%</span>',
                        "useHTML": True
                    },
                    "showInLegend": False,
                    "states": {
                        "hover": {
                            "halo": {
                                "size": 8,
                                "opacity": 0.6
                            },
                            "brightness": 0.15
                        },
                        "select": {
                            "halo": {
                                "size": 12,
                                "opacity": 0.8
                            }
                        }
                    },
                    "borderWidth": 3,
                    "borderColor": "rgba(0, 0, 0, 0.2)"
                }
            },
            "series": [{
                "name": "Hours",
                "data": pie_data
            }],
            "credits": {"enabled": False},
            "responsive": {
                "rules": [{
                    "condition": {"maxWidth": 600},
                    "chartOptions": {
                        "chart": {"height": 350},
                        "title": {"style": {"fontSize": "16px"}},
                        "plotOptions": {
                            "pie": {
                                "dataLabels": {
                                    "format": "{point.percentage:.0f}%",
                                    "distance": 15
                                }
                            }
                        }
                    }
                }]
            }
        }
        
        # For single category, adjust chart appearance
        if single_category:
            # Modify chart to look good with a single segment
            config["plotOptions"]["pie"]["innerSize"] = "60%"  # Slightly smaller donut hole
            config["plotOptions"]["pie"]["startAngle"] = 0     # Start at top
            config["plotOptions"]["pie"]["endAngle"] = 360     # Full circle
            
            # Add special styling for the single segment
            if pie_data and len(pie_data) > 0:
                pie_data[0]["borderWidth"] = 0
                pie_data[0]["brightness"] = 0
        
        # Display the donut chart
        try:
            hct.streamlit_highcharts(config, height=450)
        except Exception as e:
            st.error(f"Chart render failed: {e}")
            # Fallback to simple metrics with formatted category names
            st.dataframe(pd.DataFrame({
                'Category': ['Worked Hours' if cat == 'FIN WORKED' else cat for cat in hours_data.keys() if cat != "Other"],
                'Hours': [v for k, v in hours_data.items() if k != "Other"],
                'Percentage': [f"{(v/total_hours)*100:.1f}%" for k, v in hours_data.items() if k != "Other"]
            }))

        # Find dominant category and format display name
        # Exclude dummy category from calculations
        real_categories = {k: v for k, v in hours_data.items() if k != "Other"}
        max_category = max(real_categories.keys(), key=lambda x: real_categories[x])
        max_category_display = 'Worked Hours' if max_category == 'FIN WORKED' else max_category
        max_percentage = (real_categories[max_category] / total_hours) * 100

        # Calculate efficiency metrics
        productive_hours = hours_data.get('FIN WORKED', 0)
        non_productive_hours = total_hours - productive_hours
        efficiency_rate = (productive_hours / total_hours) * 100 if total_hours > 0 else 0

# Replace the entire summary metrics section with this:

        # Display summary metrics with formatted names and enhanced hover animations
        summary_metrics = [
            ("Top Category", max_category_display, f"{max_percentage:.1f}%"),
            ("Work Efficiency", f"{efficiency_rate:.1f}%", f"{productive_hours:,.0f}h productive"),
            ("Non-Work Hours", f"{non_productive_hours:,.0f}h", f"{((non_productive_hours/total_hours)*100):.1f}% of total")
        ]

        # First, add the CSS styles
        st.markdown("""
        <style>
        .metric-grid-item {
            background: rgba(59, 130, 246, 0.1); 
            border: 1px solid rgba(59, 130, 246, 0.3); 
            border-radius: 8px; 
            padding: 12px; 
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            transform: translateY(0px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .metric-grid-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
            background: rgba(59, 130, 246, 0.15);
            border-color: rgba(59, 130, 246, 0.5);
        }
        </style>
        """, unsafe_allow_html=True)

        # Create the grid HTML
        metrics_html = "<div style='display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 20px;'>"

        # Add each metric as a grid item
        for label, primary, secondary in summary_metrics:
            metrics_html += f"""
            <div class='metric-grid-item'>
                <div style='color: #3b82f6; font-size: 13px; font-weight: 600; text-transform: uppercase; margin-bottom: 4px;'>{label}</div>
                <div style='color: #ffffff; font-size: 18px; font-weight: 700; margin-bottom: 2px;'>{primary}</div>
                <div style='color: #94a3b8; font-size: 12px;'>{secondary}</div>
            </div>"""

        metrics_html += "</div>"

        # Display the metrics grid
        st.markdown(metrics_html, unsafe_allow_html=True)
    
    with col2:
        
        # Add some spacing
        st.markdown("<div style='margin-bottom: 20px;'></div>", unsafe_allow_html=True)
        
        # Add CSS for category cards
        st.markdown("""
        <style>
        .category-card {
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(51, 65, 85, 0.9));
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            transform: translateY(0px);
        }
        .category-card:hover {
            transform: translateY(-6px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15), 0 6px 12px rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, rgba(30, 41, 59, 1), rgba(51, 65, 85, 1));
        }
        </style>
        """, unsafe_allow_html=True)
        
        # Create detailed metrics for each category with enhanced hover effects
        for i, (category, hours) in enumerate([(cat, hours_data[cat]) for cat in sorted_categories if cat != "Other"]):
            percentage = (hours / total_hours) * 100
            color = category_colors.get(category, '#6b7280')
            
            # Format category display name
            display_category = 'Worked Hours' if category == 'FIN WORKED' else category
            
            # Create individual category card
            st.markdown(f"""
            <div class='category-card' style='border-left: 4px solid {color};'>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="flex: 1;">
                        <div style="color: {color}; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 4px;">
                            {display_category}
                        </div>
                        <div style="color: #ffffff; font-size: 20px; font-weight: 700; margin-bottom: 2px;">
                            {hours:,.0f}
                        </div>
                        <div style="color: #94a3b8; font-size: 12px;">
                            hours
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div style="color: {color}; font-size: 18px; font-weight: 700;">
                            {percentage:.1f}%
                        </div>
                        <div style="color: #94a3b8; font-size: 12px;">
                            of total
                        </div>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

            