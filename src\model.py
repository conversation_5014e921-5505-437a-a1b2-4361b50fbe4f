import streamlit as st
import pandas as pd
from charts import create_semi_circle_chart, create_activity_pie_chart, render_store_performance_charts


def model_tab(df, filtered_df, selected_country, selected_store):
    """
    Renders the Model tab content.
    
    Args:
        df: Original unfiltered DataFrame
        filtered_df: Filtered DataFrame based on current selections
        selected_country: Currently selected country
        selected_store: Currently selected store
    """
    # Calculate main metrics
    total_orders = filtered_df['Order #'].sum()
    total_sales = filtered_df['Sales'].sum()
    total_picked_items = filtered_df['Picked item #'].sum()
    picking_rate = filtered_df['Model pickrate'].sum()
    picking_hours = filtered_df['Picking hours'].sum()
    total_hours = filtered_df['Total hours'].sum()
    avg_basket_size = filtered_df['Item/order (Basket size)'].mean()
    avg_picking_time = filtered_df['Picking minutes/order'].mean()
    total_fte = filtered_df['FTE'].sum()

    # Main KPI Cards
    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown(f"""
        <div class="kpi-card primary">
            <h1 class="kpi-value">{total_orders:,.0f}</h1>
            <p class="kpi-label">Total Orders</p>
            <p class="kpi-delta">&nbsp;</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="kpi-card success">
            <h1 class="kpi-value">£{total_sales/1000:,.0f}K</h1>
            <p class="kpi-label">Total Sales</p>
            <p class="kpi-delta">£{total_sales/total_orders:.2f} per order</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown(f"""
        <div class="kpi-card info">
            <h1 class="kpi-value">{total_picked_items/1000:,.0f}K</h1>
            <p class="kpi-label">Picked Items</p>
            <p class="kpi-delta">&nbsp;</p>
        </div>
        """, unsafe_allow_html=True)

    # Secondary metrics
    st.markdown("<br>", unsafe_allow_html=True)
    col1, col2, col3, col4 = st.columns(4)

    # Calculate appropriate Model pickrate based on filters
    if st.session_state.show_store_filter:
        pick_rate_display = picking_rate
        pick_rate_subtitle = "Model"
    else:
        # If country is selected, show average for that country, otherwise global average
        if selected_country and selected_country != 'All Countries':
            country_avg = df[df['Country'] == selected_country]['Model pickrate'].mean()
            pick_rate_display = country_avg if not pd.isna(country_avg) else 0
            pick_rate_subtitle = f"{selected_country} Avg"
        else:
            pick_rate_display = df['Model pickrate'].mean() if 'Model pickrate' in df.columns else 0
            pick_rate_subtitle = "Global Avg"

    secondary_metrics = [
        ("FTE", f"{total_fte:.0f}", "Total Workforce"),
        ("Model pickrate", f"{pick_rate_display:,.0f} ", pick_rate_subtitle),
        ("Avg Picking Time", f"{avg_picking_time:.1f} min", "Per Order"),
        ("Avg Basket Size", f"{avg_basket_size:,.0f}", "Items/Order")
    ]

    for col, (label, value, subtitle) in zip([col1, col2, col3, col4], secondary_metrics):
        with col:
            st.markdown(f"""
            <div class="metric-item">
                <div class="metric-value">{value}</div>
                <div class="metric-label">{label}</div>
                <div style="font-size: 0.7rem; color: #999; margin-top: 3px;">{subtitle}</div>
            </div>
            """, unsafe_allow_html=True)

    # Activity Distribution Section
    st.markdown("<br>", unsafe_allow_html=True)
    st.markdown('<h2 class="section-header">📈 Activity Analysis</h2>', unsafe_allow_html=True)

    # Calculate activity data
    activity_columns = [
        'Marshalling',
        'Other',
        'C&C',
        'Managing colleagues',
        'Admin',
        'Prepick (GM+FF)',
        'Special activities'
    ]

    activity_sums = filtered_df[activity_columns].sum().round(2)
    pie_data = [{"name": col, "y": float(activity_sums[col])} for col in activity_columns if activity_sums[col] > 0]

    # Create two columns for charts
    col1, col2 = st.columns([2, 3])

    with col1:
        # Semi-circle chart container
        with st.container(key="semi_circle_chart"):
            # Semi-circle chart for picking efficiency
            create_semi_circle_chart(picking_hours, None, total_hours, height=350, budgettab=False)

    with col2:
        # Activity pie chart container
        with st.container(key="activity_pie_chart"):
            # Activity distribution pie chart
            create_activity_pie_chart(pie_data, height=350, legend_at_bottom=False)

    # Store Performance Section (only for country view)
    if selected_country != 'All Countries':
        st.markdown("<br>", unsafe_allow_html=True)
        st.markdown('<h2 class="section-header">🏪 Store Performance Metrics</h2>', unsafe_allow_html=True)
        
        # Get store data for the selected country
        country_stores = df[df['Country'] == selected_country].copy()
        
        # Render store performance charts using the imported function
        render_store_performance_charts(country_stores, selected_store)

        