import pandas as pd
import numpy as np
from pathlib import Path
import textwrap
import sys

def analyze_excel(file_path, sheet_name='basic', output_file=None):
    """
    Analyze an Excel file and save the analysis to a text file.
    
    Args:
        file_path (str): Path to the Excel file
        sheet_name (str): Name of the sheet to analyze (default: 'basic')
        output_file (str, optional): Path to save the analysis. If None, prints to console
    """
    def write_output(text, end='\n'):
        if output_file:
            with open(output_file, 'a', encoding='utf-8') as f:
                f.write(text + end)
        else:
            print(text, end=end)
    
    try:
        # Clear output file if it exists
        if output_file and Path(output_file).exists():
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('')
        
        # Convert to Path object
        file_path = Path(file_path)
        
        # Check if file exists
        if not file_path.exists():
            write_output(f"Error: File not found at {file_path}")
            return
        
        # Read the Excel file
        write_output(f"Analyzing Excel file: {file_path}")
        
        # Get all sheet names
        xl = pd.ExcelFile(file_path)
        write_output(f"\nAvailable sheets: {', '.join(xl.sheet_names)}")
        
        # Check if the specified sheet exists
        if sheet_name not in xl.sheet_names:
            write_output(f"\nError: Sheet '{sheet_name}' not found in the Excel file.")
            write_output(f"Available sheets are: {', '.join(xl.sheet_names)}")
            return
        
        # Read the specified sheet
        write_output(f"\n{'='*80}")
        write_output(f"ANALYZING SHEET: '{sheet_name}'")
        write_output(f"{'='*80}")
        
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        
        # Basic information
        write_output(f"\nSHAPE: {df.shape} (rows x columns)")
        write_output(f"TOTAL ENTRIES: {len(df):,}")
        write_output(f"TOTAL COLUMNS: {len(df.columns)}")
        
        # Column information
        write_output("\n" + "-"*120)
        write_output(f"{'COLUMN NAME':<30} | {'TYPE':<10} | {'NON-NULL':<9} | {'UNIQUE':<7} | SAMPLE VALUES")
        write_output("-"*120)
        
        for col in df.columns:
            # Get sample values (first 3 non-null values)
            sample_values = df[col].dropna().head(3).tolist()
            sample_str = ", ".join([str(x)[:20] + ('...' if len(str(x)) > 20 else '') for x in sample_values])
            
            # Truncate long sample strings
            if len(sample_str) > 40:
                sample_str = sample_str[:37] + '...'
                
            write_output(f"{col[:27]:<30} | {str(df[col].dtype):<10} | {df[col].count():<7,} | {df[col].nunique():<6,} | {sample_str}")
        
        # Numeric columns statistics
        numeric_cols = df.select_dtypes(include=['number']).columns
        if not numeric_cols.empty:
            write_output("\n" + "="*80)
            write_output("NUMERIC COLUMNS STATISTICS")
            write_output("="*80)
            
            # Calculate statistics
            stats = df[numeric_cols].describe(percentiles=[.25, .5, .75]).T
            stats = stats.rename(columns={
                '25%': '25th %',
                '50%': 'median',
                '75%': '75th %',
                'count': 'count',
                'mean': 'mean',
                'std': 'std',
                'min': 'min',
                'max': 'max'
            })
            
            # Reorder columns
            stats = stats[['count', 'mean', 'std', 'min', '25th %', 'median', '75th %', 'max']]
            
            # Format numbers for better readability
            pd.set_option('display.float_format', '{:,.2f}'.format)
            stats_str = stats.to_string()
            
            # Write statistics in chunks to handle large outputs
            for line in stats_str.split('\n'):
                write_output(line)
            
            # Reset display options
            pd.reset_option('display.float_format')
        
        # Check for missing values
        missing_values = df.isnull().sum()
        missing_values = missing_values[missing_values > 0]
        if not missing_values.empty:
            write_output("\n" + "="*80)
            write_output("MISSING VALUES")
            write_output("="*80)
            missing_pct = (missing_values / len(df)) * 100
            missing_df = pd.DataFrame({
                'Missing Count': missing_values,
                'Missing %': missing_pct
            })
            write_output(missing_df.sort_values('Missing Count', ascending=False).to_string())
        else:
            write_output("\n" + "="*80)
            write_output("MISSING VALUES")
            write_output("="*80)
            write_output("No missing values found in the dataset.")
        
        # Display first few rows of data
        write_output("\n" + "="*80)
        write_output("SAMPLE DATA (first 5 rows)")
        write_output("="*80)
        
        # Temporarily adjust display settings
        with pd.option_context('display.max_columns', None, 'display.width', 1000, 'display.max_colwidth', 30):
            sample_data = df.head().to_string()
            for line in sample_data.split('\n'):
                write_output(line)
        
        # Display unique values for object/categorical columns
        object_cols = df.select_dtypes(include=['object']).columns
        if not object_cols.empty:
            write_output("\n" + "="*80)
            write_output("UNIQUE VALUES IN CATEGORICAL COLUMNS")
            write_output("="*80)
            
            for col in object_cols:
                unique_vals = df[col].unique()
                write_output(f"\n{col} ({len(unique_vals)} unique values):")
                
                # Split into chunks to avoid line length issues
                vals_str = ', '.join(map(str, unique_vals))
                for line in textwrap.wrap(vals_str, width=100):
                    write_output(f"  {line}")
        
        write_output("\n" + "="*80)
        write_output("ANALYSIS COMPLETE")
        write_output("="*80)
        
    except Exception as e:
        error_msg = f"\nAn error occurred: {str(e)}"
        write_output(error_msg)
        import traceback
        write_output(traceback.format_exc())

if __name__ == "__main__":
    # Path to the Excel file
    excel_file = r"C:\Users\<USER>\Desktop\prog\p\dotcom_WebApp\raw_files\25q2_Dotcom_for html.xlsx"
    output_file = r"C:\Users\<USER>\Desktop\prog\p\dotcom_WebApp\excel_analysis.txt"
    
    # Run the analysis
    print(f"Starting Excel analysis. Results will be saved to: {output_file}")
    analyze_excel(excel_file, sheet_name='basic', output_file=output_file)
    print("Analysis complete. Check the output file for results.")
