import streamlit as st
import pandas as pd
import numpy as np
import streamlit_highcharts as hct
from charts import create_dotcom_sales_chart, create_total_hours_chart, create_semi_circle_chart, create_activity_pie_chart

# Add after the load_budget_data() function
def get_period_week_info():
    """
    Calculate current period and week based on the date logic provided.
    Week 1 of Period 1 is only 2 days (March 1-2)
    Week numbers continue sequentially 1-53 across all periods.
    Returns current period and absolute week number.
    """
    from datetime import datetime, timedelta
    
    # Define period lengths
    period_weeks = {
        1: 5, 2: 4, 3: 4, 4: 5, 5: 4, 6: 4,
        7: 5, 8: 4, 9: 4, 10: 5, 11: 4, 12: 4
    }
    
    # Start date for period 1 week 1 (March 1, 2025)
    start_date = datetime(2025, 3, 1)
    current_date = datetime.now()
    
    # For dates before March 1, 2025, assume we're in the previous fiscal year's last period
    if current_date < start_date:
        return 12, 53  # Last week of the fiscal year
    
    # Calculate days since start
    days_elapsed = (current_date - start_date).days
    
    # Special handling for Period 1 Week 1 (only 2 days)
    if days_elapsed < 2:
        return 1, 1
    
    # Calculate which week we're in (absolute week number)
    adjusted_days = days_elapsed - 2  # Subtract the 2 days of week 1
    absolute_week = (adjusted_days // 7) + 2  # +2 because week 1 is already counted
    
    # Now find which period this week belongs to
    week_count = 0
    for period, weeks in period_weeks.items():
        if absolute_week <= week_count + weeks:
            return period, absolute_week
        week_count += weeks
    
    # If we're past all periods, return the last period and week
    return 12, 53

def get_period_date_range(period, week):
    """
    Get the date range for a specific period and absolute week number.
    Week 1 of Period 1 is March 1-2 (2 days only)
    Week parameter is the absolute week number (1-53)
    """
    from datetime import datetime, timedelta
    
    # Start date for period 1 week 1
    start_date = datetime(2025, 3, 1)
    
    # Special case for Week 1
    if week == 1:
        return start_date, datetime(2025, 3, 2)
    
    # For all other weeks, calculate normally
    days_to_add = 2  # Start with 2 days for week 1
    days_to_add += (week - 2) * 7  # Add full weeks after week 1
    
    week_start = start_date + timedelta(days=days_to_add)
    
    # Calculate week end
    if week == 53:
        # Last week of the fiscal year ends on February 28/29
        year = start_date.year + 1
        if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0):
            week_end = datetime(year, 2, 29)
        else:
            week_end = datetime(year, 2, 28)
    else:
        week_end = week_start + timedelta(days=6)
    
    return week_start, week_end

def get_fiscal_year_week_count():
    """
    Calculate total number of weeks in the fiscal year.
    Should be 53 weeks (1 short week + 52 full weeks)
    """
    period_weeks = {
        1: 5, 2: 4, 3: 4, 4: 5, 5: 4, 6: 4,
        7: 5, 8: 4, 9: 4, 10: 5, 11: 4, 12: 4
    }
    return sum(period_weeks.values())  # Should equal 53

def on_period_change():
    st.session_state.selected_period = st.session_state.period_select_widget
    # Reset week to 1 when period changes
    st.session_state.selected_week = 1

def on_week_change():
    st.session_state.selected_week = st.session_state.week_select_widget


def budget_tab(budget_df):
    """Main function to render the Budget tab with charts and filters"""
    # Create a two-column layout for the Budget tab
    # Create a three-column layout for the Budget tab
    # Initial unfiltered data - will be filtered after user selections


    with st.container(key="budget_top_part"):
    # Replace the existing chart section with this improved layout
        col1, col2 = st.columns([4, 1])

        with col1:
            # Main charts in left column
            create_dotcom_sales_chart(budget_df)



        with col2:
            # Right column for filters and smaller charts
            st.markdown("### Budget Filters")
            
            # Create a container for budget filters with custom styling
            with st.container():


                week_start, week_end = get_period_date_range(st.session_state.selected_period, st.session_state.selected_week)
                st.markdown(f"""
                    <div style="
                        background: rgba(59, 130, 246, 0.2);
                        border: 1px solid rgba(59, 130, 246, 0.5);
                        border-radius: 8px;
                        padding: 8px;
                        margin-top: 10px;
                        text-align: center;
                    ">
                        <div style="
                            color: #60a5fa;
                            font-size: 14px;
                            font-weight: 600;
                        ">📆 Date Range</div>
                        <div style="
                            color: #ffffff;
                            font-size: 13px;
                        ">{week_start.strftime('%B %d')} - {week_end.strftime('%B %d, %Y')}</div>
                    </div>
                    """, unsafe_allow_html=True)
                
                # Get current period and week
                current_period, current_week = get_period_week_info()
                
                # Period filter
                periods = list(range(1, 13))
                selected_period = st.selectbox(
                    'Period',
                    periods,
                    index=periods.index(st.session_state.selected_period),
                    key='period_select_widget',
                    on_change=on_period_change,
                    format_func=lambda x: f"Period {x} {'(Current)' if x == current_period else ''}"
                )
                
                # Week filter calculation (keep existing logic)
                period_weeks_map = {
                    1: 5, 2: 4, 3: 4, 4: 5, 5: 4, 6: 4,
                    7: 5, 8: 4, 9: 4, 10: 5, 11: 4, 12: 4
                }
                
                start_week = 1
                for p in range(1, st.session_state.selected_period):
                    start_week += period_weeks_map[p]
                
                weeks_in_period = period_weeks_map[st.session_state.selected_period]
                weeks = list(range(start_week, start_week + weeks_in_period))
                
                if st.session_state.selected_week not in weeks:
                    st.session_state.selected_week = weeks[0]
                
                selected_week = st.selectbox(
                    'Week',
                    weeks,
                    index=weeks.index(st.session_state.selected_week),
                    key='week_select_widget',
                    on_change=on_week_change,
                    format_func=lambda x: f"Week {x} {'(Current)' if st.session_state.selected_period == current_period and x == current_week else ''}"
                )

                # Add callback functions for navigation
                def prev_period():
                    if st.session_state.selected_period > 1:
                        st.session_state.selected_period -= 1
                        period_weeks_map = {
                            1: 5, 2: 4, 3: 4, 4: 5, 5: 4, 6: 4,
                            7: 5, 8: 4, 9: 4, 10: 5, 11: 4, 12: 4
                        }
                        start_week = 1
                        for p in range(1, st.session_state.selected_period):
                            start_week += period_weeks_map[p]
                        st.session_state.selected_week = start_week

                def next_period():
                    if st.session_state.selected_period < 12:
                        st.session_state.selected_period += 1
                        period_weeks_map = {
                            1: 5, 2: 4, 3: 4, 4: 5, 5: 4, 6: 4,
                            7: 5, 8: 4, 9: 4, 10: 5, 11: 4, 12: 4
                        }
                        start_week = 1
                        for p in range(1, st.session_state.selected_period):
                            start_week += period_weeks_map[p]
                        st.session_state.selected_week = start_week

                def prev_week():
                    current_week_idx = weeks.index(st.session_state.selected_week)
                    if current_week_idx > 0:
                        st.session_state.selected_week = weeks[current_week_idx - 1]

                def next_week():
                    current_week_idx = weeks.index(st.session_state.selected_week)
                    if current_week_idx < len(weeks) - 1:
                        st.session_state.selected_week = weeks[current_week_idx + 1]

                # Period Navigation Row
                period_col1, period_col2, period_col3 = st.columns([1, 2, 1])
                
                with period_col1:
                    st.button("◀", key="prev_period_btn", on_click=prev_period, 
                             disabled=st.session_state.selected_period <= 1,
                             help="Previous Period",
                             use_container_width=True)
                
                with period_col2:
                    st.markdown(f"""
                    <div style="
                        text-align: center;
                        padding: 8px;
                        background: rgba(59, 130, 246, 0.2);
                        border-radius: 6px;
                        color: white;
                        font-weight: 600;
                        margin-top: 0px;
                    ">
                        Period {st.session_state.selected_period}
                    </div>
                    """, unsafe_allow_html=True)
                
                with period_col3:
                    st.button("▶", key="next_period_btn", on_click=next_period, 
                             disabled=st.session_state.selected_period >= 12,
                             help="Next Period",
                             use_container_width=True)

                # Week Navigation Row
                week_col1, week_col2, week_col3 = st.columns([1, 2, 1])
                
                current_week_idx = weeks.index(st.session_state.selected_week)
                
                with week_col1:
                    st.button("◀", key="prev_week_btn", on_click=prev_week, 
                             disabled=current_week_idx <= 0,
                             help="Previous Week",
                             use_container_width=True)
                
                with week_col2:
                    st.markdown(f"""
                    <div style="
                        text-align: center;
                        padding: 8px;
                        background: rgba(139, 92, 246, 0.2);
                        border-radius: 6px;
                        color: white;
                        font-weight: 600;
                        margin-top: 0px;
                    ">
                        Week {st.session_state.selected_week}
                    </div>
                    """, unsafe_allow_html=True)
                
                with week_col3:
                    st.button("▶", key="next_week_btn", on_click=next_week, 
                             disabled=st.session_state.selected_week >= weeks[-1],
                             help="Next Week",
                             use_container_width=True)
                    


                # Initialize session state if not exists
                if 'period_only_view' not in st.session_state:
                    st.session_state.period_only_view = False

                # Callback function for toggle
                def on_period_only_toggle():
                    st.session_state.period_only_view = st.session_state.period_only_toggle_widget

                col1, col2 = st.columns([1, 3])

                with col1:
                    period_only_view = st.toggle(
                        '', 
                        value=st.session_state.period_only_view,
                        key='period_only_toggle_widget',
                        help="Toggle between showing only selected period weeks vs entire year",
                        on_change=on_period_only_toggle
                    )

                with col2:

                    # Visual indicator for the toggle state
                    if st.session_state.period_only_view:
                        st.markdown(f"""
                        <div style="
                            background: rgba(16, 185, 129, 0.2);
                            border: 1px solid rgba(16, 185, 129, 0.4);
                            border-radius: 6px;
                            padding: 6px;
                            text-align: center;
                            margin: 8px 0;
                            font-size: 12px;
                            color: #10b981;
                            font-weight: 600;
                        ">
                            📊 Period {st.session_state.selected_period} View
                        </div>
                        """, unsafe_allow_html=True)
                    else:
                        st.markdown(f"""
                        <div style="
                            background: rgba(139, 92, 246, 0.2);
                            border: 1px solid rgba(139, 92, 246, 0.4);
                            border-radius: 6px;
                            padding: 6px;
                            text-align: center;
                            margin: 8px 0;
                            font-size: 12px;
                            color: #8b5cf6;
                            font-weight: 600;
                        ">
                            📈 Full Year View
                        </div>
                        """, unsafe_allow_html=True)


                # Now that we have the user's selections, create the filtered dataset
                filtered_budget_df = budget_df.copy()
                
                # Apply period and week filters
                if 'PERIOD' in filtered_budget_df.columns and hasattr(st.session_state, 'selected_period'):
                    filtered_budget_df = filtered_budget_df[filtered_budget_df['PERIOD'] == st.session_state.selected_period]
                
                if 'WEEK' in filtered_budget_df.columns and hasattr(st.session_state, 'selected_week'):
                    filtered_budget_df = filtered_budget_df[filtered_budget_df['WEEK'] == st.session_state.selected_week]
                
                # Apply country filter if available
                if hasattr(st.session_state, 'selected_country') and st.session_state.selected_country != 'All Countries':
                    if 'COUNTRY' in filtered_budget_df.columns:
                        filtered_budget_df = filtered_budget_df[filtered_budget_df['COUNTRY'] == st.session_state.selected_country]
                
                # Apply store filter if available
                if hasattr(st.session_state, 'selected_store') and hasattr(st.session_state, 'show_store_filter'):
                    if st.session_state.selected_store != 'All Stores' and st.session_state.show_store_filter:
                        if 'SHOP' in filtered_budget_df.columns:
                            store_number = st.session_state.selected_store.split(' ')[0]
                            filtered_budget_df = filtered_budget_df[filtered_budget_df['SHOP'].astype(str) == store_number]
    

    with st.container(key="budget_bottom_part"):
        col1, col2 = st.columns([3, 2])

        with col1:
            # with st.container(key="budget_total_hours_chart"):
                create_total_hours_chart(budget_df)

        with col2:


            if not filtered_budget_df.empty:
                # Activity pie chart
                activity_columns_budget = [
                    'Marshalling', 'Admin Hours', 'Managing hours', 
                    'Other', 'C&C', 'Training Hours', 'Prepick GM F&F', "paid shift (cz sk)"
                ]
                
                existing_columns = [col for col in activity_columns_budget if col in filtered_budget_df.columns]
                
                if existing_columns:
                    activity_sums_budget = filtered_budget_df[existing_columns].sum().round(2)
                    pie_data_budget = [{"name": col.title(), "y": float(activity_sums_budget[col])} 
                                    for col in existing_columns if activity_sums_budget[col] > 0]
                    
                    if pie_data_budget:
                        with st.container(key="budget_activity_pie_chart"):
                            # Pass budgettab=True to show slice names instead of legend
                            create_activity_pie_chart(pie_data_budget, height=350, budgettab=True)

                # Semi-circle chart
                if 'Picking Hours' in filtered_budget_df.columns and 'FINAL hours' in filtered_budget_df.columns:
                    picking_hours_budget = filtered_budget_df['Picking Hours'].sum()
                    total_worked_hours = filtered_budget_df['FINAL hours'].sum()
                    backroom_hours = filtered_budget_df['Backroom hours'].sum()
                    
                    if total_worked_hours > 0:
                        with st.container(key="budget_semi_circle_chart"):
                            # Pass budgettab=True for proper spacing
                            create_semi_circle_chart(picking_hours_budget, backroom_hours, total_worked_hours, height=150, budgettab=True)

            else:
                st.write("No data available for the selected period and week.")
