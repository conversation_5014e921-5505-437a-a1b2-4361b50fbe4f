import streamlit as st
import pandas as pd
import numpy as np
import polars as pl
import streamlit_highcharts as hct
from pathlib import Path
from charts import create_semi_circle_chart, create_activity_pie_chart, create_enhanced_bar_chart, render_store_performance_charts
from styles import get_main_styles, get_tabs_styles
from budget import budget_tab, get_period_week_info
from model import model_tab

# Set page configuration
st.set_page_config(
    page_title="DotCom WebApp",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Simple viewport control without aggressive JavaScript
st.markdown("""
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<style>
    /* Additional scaling control */
    iframe[title="streamlit_app"] {
        zoom: 1 !important;
        transform: scale(1) !important;
    }
</style>
""", unsafe_allow_html=True)

selected_quarter = "Q2 2025"

# Load data on Model tab
@st.cache_data
def load_data():
    # Path to the Excel file (relative to the script location)
    # base_dir = Path(__file__).parent.parent  # Go up one level from src/
    # excel_path = base_dir / 'raw_files' / '25q2_Dotcom_for html.xlsx'
    excel_path = '/mnt/tbs_enterprise_analytics/server/dotcom_webapp/25q2_Dotcom_for html.xlsx'
    # Read the 'basic' sheet
    df = pd.read_excel(excel_path, sheet_name='basic')
    return df

# Load data on Budget tab
@st.cache_data
def load_budget_data():
    # Path to the Excel file (relative to the script location)
    # base_dir = Path(__file__).parent.parent  # Go up one level from src/
    # excel_path = base_dir / 'raw_files' / '25q2_Dotcom budgeted hours_for html.xlsx'
    # Read the 'basic' sheet lazily
    excel_path = '/mnt/tbs_enterprise_analytics/server/dotcom_webapp/25q2_Dotcom budgeted hours_for html.xlsx'
    df = pl.read_excel(excel_path, sheet_name='picker')
    # Filter rows where FINAL hours > 0
    df_filtered = df.filter(pl.col("FINAL hours") > 0)
    df_filtered = df_filtered.with_columns(pl.col("SHOP").cast(pl.String))
    
    return df_filtered.to_pandas()


# Initialize session state for filters
if 'selected_country' not in st.session_state:
    st.session_state.selected_country = 'All Countries'
if 'selected_store' not in st.session_state:
    st.session_state.selected_store = None
if 'show_store_filter' not in st.session_state:
    st.session_state.show_store_filter = False
# Add these after the existing session state initializations
if 'selected_period' not in st.session_state:
    current_period, current_week = get_period_week_info()
    st.session_state.selected_period = current_period
if 'selected_week' not in st.session_state:
    current_period, current_week = get_period_week_info()
    st.session_state.selected_week = current_week
if 'period_only_view' not in st.session_state:
    st.session_state.period_only_view = False

df = load_data()
budget_df = load_budget_data()

# Callback functions
def on_toggle_change():
    st.session_state.show_store_filter = st.session_state.store_toggle_widget

def on_country_change():
    st.session_state.selected_country = st.session_state.country_select_widget
    st.session_state.selected_store = None

def on_store_change():
    selected = st.session_state.store_select_widget
    st.session_state.selected_store = selected
    
    # Auto-update country if needed
    if selected:
        store_number = int(selected.split(' ')[0])
        store_country = df[df['Store'] == store_number]['Country'].iloc[0]
        if store_country != st.session_state.selected_country and st.session_state.selected_country != 'All Countries':
            st.session_state.selected_country = store_country


# Sidebar - Filters with enhanced styling

pcs_dir = Path(__file__).parent
with st.sidebar:
    st.image(pcs_dir / "pcs" / "Tesco_logo.png", use_container_width=True)
    st.markdown("""
        <div style="
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.9));
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">
            <div style="
                display: flex;
                flex-direction: column;
                gap: 8px;
                text-align: center;">
                <div style="
                    color: #93c5fd;
                    font-size: 14px;
                    font-weight: 500;">
                    Model
                </div>
                <div style="
                    color: white;
                    font-size: 18px;
                    font-weight: 600;">
                    Q2-25
                </div>
                <div style="
                    margin-top: 5px;
                    height: 1px;
                    background: linear-gradient(90deg, 
                        transparent, 
                        rgba(59, 130, 246, 0.2), 
                        transparent);">
                </div>
                <div style="
                    color: #93c5fd;
                    font-size: 14px;
                    font-weight: 500;">
                    Budget
                </div>
                <div style="
                    color: white;
                    font-size: 18px;
                    font-weight: 600;">
                    P8-25
                </div>
            </div>
        </div>
    """, unsafe_allow_html=True)




# Get unique countries and sort them
countries = ['All Countries'] + sorted(df['Country'].unique().tolist())

# Country filter
selected_country = st.sidebar.selectbox(
    'Country', 
    countries, 
    index=countries.index(st.session_state.selected_country),
    key='country_select_widget',
    on_change=on_country_change
)

# Apply country filter
if st.session_state.selected_country != 'All Countries':
    filtered_df = df[df['Country'] == st.session_state.selected_country].copy()
else:
    filtered_df = df.copy()

# Toggle for store filter - only show when a specific country is selected
if st.session_state.selected_country != 'All Countries':
    show_store_filter = st.sidebar.toggle(
        'Filter by Store', 
        value=st.session_state.show_store_filter,
        key='store_toggle_widget',
        on_change=on_toggle_change
    )
else:
    # Reset store filter when 'All Countries' is selected
    show_store_filter = False
    st.session_state.show_store_filter = False

# Store filter
if show_store_filter:
    # Create store options based on current country filter
    if st.session_state.selected_country != 'All Countries':
        store_df = df[df['Country'] == st.session_state.selected_country]
    else:
        store_df = df
    
    store_options = sorted([f"{row['Store']} {row['Name']}" 
                         for _, row in store_df[['Store', 'Name']].drop_duplicates().iterrows()])
    
    # Check if current store selection is valid for the current country
    if st.session_state.selected_store not in store_options and store_options:  # Only update if there are store options
        st.session_state.selected_store = store_options[0]  # Select first store by default
    
    # Store selectbox
    selected_store = st.sidebar.selectbox(
        'Select Store', 
        store_options,
        index=store_options.index(st.session_state.selected_store) if st.session_state.selected_store in store_options else 0,
        key='store_select_widget',
        on_change=on_store_change
    )
    
    # Apply store filter
    if st.session_state.selected_store:
        store_number = int(st.session_state.selected_store.split(' ')[0])
        filtered_df = filtered_df[filtered_df['Store'] == store_number].copy()
else:
    selected_store = None
    st.session_state.selected_store = None

# Add visual separator after main filters
st.sidebar.markdown("""
<div style="
    border-bottom: 2px solid rgba(59, 130, 246, 0.2);
    margin: 20px 0;
"></div>
""", unsafe_allow_html=True)

# Add info button at the bottom of the sidebar
st.sidebar.markdown(f"""
<style>
    .info-button {{
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.9));
        border: 2px solid rgba(59, 130, 246, 0.4);
        color: white;
        font-size: 20px;
        cursor: help;
        position: relative;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
    }}
    
    .info-button:hover {{
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 1));
    }}
    
    .info-tooltip {{
        visibility: hidden;
        position: absolute;
        bottom: 45px;
        left: 0;
        width: 280px;
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(15, 23, 42, 0.98));
        color: #fff;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(59, 130, 246, 0.4);
        opacity: 0;
        transform: translateY(10px);
        transition: opacity 0.3s, transform 0.3s, visibility 0.3s;
        z-index: 1000;
    }}
    
    .info-tooltip p {{
        margin: 8px 0;
        font-size: 13px;
        line-height: 1.4;
    }}
    
    .info-tooltip a {{
        color: #60a5fa;
        text-decoration: none;
        transition: color 0.2s;
    }}
    
    .info-tooltip a:hover {{
        color: #93c5fd;
        text-decoration: underline;
    }}
    
    .info-tooltip strong {{
        color: #60a5fa;
        font-weight: 600;
    }}
    
    .info-button:hover .info-tooltip {{
        visibility: visible;
        opacity: 1;
        transform: translateY(0);
    }}
    
    .sidebar-info-container {{
        position: fixed;
        bottom: 20px;
        left: 10px;
        z-index: 999;
    }}
</style>

<div class='sidebar-info-container'>
    <div class="info-button" title="Dashboard Information">
        ℹ️
        <div class="info-tooltip">
            <p><strong>Data Source:</strong> {selected_quarter} budget version</p>
            <p><strong>Data Questions:</strong> Agnes Kallo-Sikos<br/><a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p><strong>Created by:</strong> Peter Hrubos<br/><a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</div>
""", unsafe_allow_html=True)

# Use session state values for the rest of the app
selected_country = st.session_state.selected_country
selected_store = st.session_state.selected_store

# Add custom CSS for dark theme
st.markdown(get_main_styles(), unsafe_allow_html=True)


# Main page - Title
st.title('📊 DotCom Performance Dashboard')

# Update the title based on store filter and country selection
if st.session_state.show_store_filter and selected_store != 'All Stores':
    store_number = selected_store.split(' ')[0]  # Get store number
    store_name = ' '.join(selected_store.split(' ')[1:])  # Get store name without the number
    st.markdown(f"### {selected_country} - {store_number} {store_name}")
else:
    st.markdown(f"### {'Central Europe Overview' if selected_country == 'All Countries' else f'{selected_country} Overview'}")

# Add custom CSS for tabs
st.markdown(get_tabs_styles(), unsafe_allow_html=True)

# Create tabs
tab1, tab2 = st.tabs(["Model", "Budget"])

with tab1:
    # Call the model_tab function from model.py
    model_tab(df, filtered_df, selected_country, selected_store)

with tab2:
    budget_tab(budget_df)
    
st.markdown(
    f"""
    <div class='footer-text'>
        <p>DotCom Performance Dashboard | Data: {selected_quarter}</p>
    </div>
    """,
    unsafe_allow_html=True
)
