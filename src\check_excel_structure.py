import pandas as pd
import numpy as np
from pathlib import Path
import textwrap

def check_excel_structure(file_path, sheet_name='basic'):
    """
    Check and display the structure of an Excel file's sheet.
    
    Args:
        file_path (str): Path to the Excel file
        sheet_name (str): Name of the sheet to check (default: 'basic')
    """
    try:
        # Convert to Path object for better path handling
        file_path = Path(file_path)
        
        # Check if file exists
        if not file_path.exists():
            print(f"Error: File not found at {file_path}")
            return
            
        # Read the Excel file
        print(f"Reading Excel file: {file_path}")
        
        # Get all sheet names
        xl = pd.ExcelFile(file_path)
        print(f"\nAvailable sheets in the Excel file: {xl.sheet_names}")
        
        # Check if the specified sheet exists
        if sheet_name not in xl.sheet_names:
            print(f"\nError: Sheet '{sheet_name}' not found in the Excel file.")
            print(f"Available sheets are: {xl.sheet_names}")
            return
            
        # Read the specified sheet
        print(f"\nReading sheet: '{sheet_name}'")
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        
        # Display basic information
        print("\n" + "="*80)
        print(f"SHEET: {sheet_name}")
        print("="*80)
        print(f"Shape: {df.shape} (rows x columns)")
        print(f"Total entries: {len(df):,}")
        print(f"Total columns: {len(df.columns)}")
        
        # Display column information
        print("\n" + "-"*120)
        print(f"{'COLUMN NAME':<30} | {'TYPE':<10} | {'NON-NULL':<9} | {'UNIQUE':<7} | SAMPLE VALUES")
        print("-"*120)
        
        for col in df.columns:
            # Get sample values (first 3 non-null values)
            sample_values = df[col].dropna().head(3).tolist()
            sample_str = ", ".join([str(x)[:20] + ('...' if len(str(x)) > 20 else '') for x in sample_values])
            
            # Truncate long sample strings
            if len(sample_str) > 40:
                sample_str = sample_str[:37] + '...'
                
            print(f"{col[:27]:<30} | {str(df[col].dtype):<10} | {df[col].count():<7,} | {df[col].nunique():<6,} | {sample_str}")
        
        # Display basic statistics for numeric columns
        numeric_cols = df.select_dtypes(include=['number']).columns
        if not numeric_cols.empty:
            print("\n" + "="*80)
            print("NUMERIC COLUMNS STATISTICS")
            print("="*80)
            
            # Get statistics and format nicely
            stats = df[numeric_cols].describe(percentiles=[.25, .5, .75]).T
            stats = stats.rename(columns={
                '25%': '25th %',
                '50%': 'median',
                '75%': '75th %',
                'count': 'count',
                'mean': 'mean',
                'std': 'std',
                'min': 'min',
                'max': 'max'
            })
            
            # Reorder columns for better readability
            stats = stats[['count', 'mean', 'std', 'min', '25th %', 'median', '75th %', 'max']]
            pd.set_option('display.float_format', '{:.2f}'.format)
            print(stats.to_string())
            
            # Reset display options
            pd.reset_option('display.float_format')
        
        # Check for missing values
        missing_values = df.isnull().sum()
        missing_values = missing_values[missing_values > 0]
        if not missing_values.empty:
            print("\n" + "="*80)
            print("MISSING VALUES")
            print("="*80)
            missing_pct = (missing_values / len(df)) * 100
            missing_df = pd.DataFrame({
                'Missing Count': missing_values,
                'Missing %': missing_pct
            })
            print(missing_df.sort_values('Missing Count', ascending=False).to_string())
        else:
            print("\n" + "="*80)
            print("MISSING VALUES")
            print("="*80)
            print("No missing values found in the dataset.")
        
        # Display first few rows of data
        print("\n" + "="*80)
        print("SAMPLE DATA (first 5 rows)")
        print("="*80)
        with pd.option_context('display.max_columns', None, 'display.width', 1000, 'display.max_colwidth', 30):
            print(df.head().to_string())
            
        # Display unique values for object/categorical columns
        object_cols = df.select_dtypes(include=['object']).columns
        if not object_cols.empty:
            print("\n" + "="*80)
            print("UNIQUE VALUES IN CATEGORICAL COLUMNS")
            print("="*80)
            for col in object_cols:
                unique_vals = df[col].unique()
                print(f"\n{col} ({len(unique_vals)} unique values):")
                # Print in columns for better readability
                print(textwrap.fill(', '.join(map(str, unique_vals)), width=100))
        
    except Exception as e:
        print(f"\nAn error occurred: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Path to the Excel file
    excel_file = r"C:\Users\<USER>\Desktop\prog\p\dotcom_WebApp\raw_files\25q2_Dotcom_for html.xlsx"
    
    # Run the check
    check_excel_structure(excel_file, sheet_name='basic')
