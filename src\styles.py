"""
This module contains the CSS styles for the DotCom WebApp.
"""

def get_main_styles():
    """
    Returns the main CSS styles for the application.
    """
    return """
<style>




    /* Scaling control - safer approach */
    .stApp {
        zoom: 1 !important;
        transform: scale(1) !important;
        transform-origin: top left !important;
    }

    /* Main container scaling */
    .main .block-container {
        zoom: 1 !important;
        transform: scale(1) !important;
        transform-origin: top left !important;
    }

    /* Force consistent text sizing */
    * {
        -webkit-text-size-adjust: 100% !important;
        -ms-text-size-adjust: 100% !important;
        text-size-adjust: 100% !important;
    }
    /* Import modern font */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
    
    /* CSS Variables for consistent theming */
    :root {
        --primary-color: #3b82f6;
        --primary-dark: #2563eb;
        --success-color: #10b981;
        --success-dark: #059669;
        --info-color: #8b5cf6;
        --info-dark: #7c3aed;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
        --bg-tertiary: #334155;
        --text-primary: #f1f5f9;
        --text-secondary: #cbd5e1;
        --text-tertiary: #94a3b8;
        --border-color: rgba(148, 163, 184, 0.1);
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    
    /* Global styles */
    .stApp {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background-color: var(--bg-primary);
    }
    
    /* Main container */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 3rem;
        max-width: 1400px;
    }
    
    /* Enhanced KPI Cards with glassmorphism effect */
    .kpi-card {
        background: rgba(30, 41, 59, 0.8);
        backdrop-filter: blur(10px);
        border: 1px solid var(--border-color);
        border-radius: 16px;
        padding: 28px;
        box-shadow: var(--shadow-lg);
        text-align: center;
        height: 100%;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .kpi-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    /* Primary (blue) card top line */
    .kpi-card.primary::before {
        background: linear-gradient(90deg, #1d4ed8, #1e40af);
    }
    
    /* Success (green) card top line */
    .kpi-card.success::before {
        background: linear-gradient(90deg, #047857, #065f46);
    }
    
    /* Info (purple) card top line */
    .kpi-card.info::before {
        background: linear-gradient(90deg, #6d28d9, #5b21b6);
    }
    
    .kpi-card:hover::before {
        opacity: 1;
    }
    
    .kpi-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl), 0 0 30px rgba(59, 130, 246, 0.2);
    }
    
    .kpi-card.primary {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(37, 99, 235, 0.9));
        backdrop-filter: blur(10px);
    }
    
    .kpi-card.success {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(5, 150, 105, 0.9));
        backdrop-filter: blur(10px);
    }
    
    .kpi-card.info {
        background: linear-gradient(135deg, rgba(139, 92, 246, 0.9), rgba(124, 58, 237, 0.9));
        backdrop-filter: blur(10px);
    }
    
    .kpi-value {
        font-size: 3rem;
        font-weight: 800;
        margin: 0;
        line-height: 1;
        letter-spacing: -0.02em;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .kpi-label {
        font-size: 0.875rem;
        font-weight: 600;
        opacity: 0.95;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-top: 12px;
        color: white;
    }
    
    .kpi-delta {
        font-size: 0.875rem;
        margin-top: 8px;
        opacity: 0.9;
        font-weight: 500;
        color: white;
    }
    
    /* Enhanced metric items with hover effects */
    .metric-item {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(51, 65, 85, 0.9));
        backdrop-filter: blur(10px);
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        border: 1px solid var(--border-color);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    
    .metric-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: linear-gradient(180deg, var(--primary-color), var(--primary-dark));
        transition: width 0.3s ease;
    }
    
    .metric-item:hover {
        transform: translateX(4px);
        box-shadow: var(--shadow-lg);
        border-color: rgba(59, 130, 246, 0.3);
    }
    
    .metric-item:hover::before {
        width: 8px;
    }
    
    .metric-value {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--text-primary);
        letter-spacing: -0.01em;
    }
    
    .metric-label {
        font-size: 0.75rem;
        color: var(--text-secondary);
        font-weight: 600;
        text-transform: uppercase;
        margin-top: 8px;
        letter-spacing: 0.05em;
    }
    
    /* Enhanced section headers */
    .section-header {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 2rem;
        position: relative;
        padding-bottom: 1rem;
        letter-spacing: -0.01em;
    }
    
    .section-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
        border-radius: 2px;
    }
    
    /* Enhanced chart containers */
    .chart-container,
    .st-key-semi_circle_chart,
    .st-key-activity_pie_chart,
    .st-key-budget_charts,
    .st-key-store_chart_1,
    .st-key-store_chart_2,
    .st-key-store_chart_3,
    .st-key-store_chart_4,
    .st-key-store_chart_5,
    .st-key-store_chart_6,
    .st-key-store_chart_picked_items {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(51, 65, 85, 0.95));
        backdrop-filter: blur(10px);
        padding: 24px;
        border-radius: 16px;
        box-shadow: var(--shadow-lg);
        margin-bottom: 24px;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }
    
    .chart-container:hover,
    .st-key-semi_circle_chart:hover,
    .st-key-activity_pie_chart:hover,
    .st-key-budget_charts:hover,
    .st-key-store_chart_1:hover,
    .st-key-store_chart_2:hover,
    .st-key-store_chart_3:hover,
    .st-key-store_chart_4:hover,
    .st-key-store_chart_5:hover,
    .st-key-store_chart_6:hover {
        box-shadow: var(--shadow-xl);
        border-color: rgba(59, 130, 246, 0.2);
    }
    
    /* Enhanced tabs */
    .stTabs [data-baseweb="tab-list"] {
        background: transparent;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding: 0;
        gap: 0;
        position: relative;
        display: flex;
        width: 100%;
    }
    
    .stTabs [data-baseweb="tab"] {
        background: transparent;
        color: var(--text-secondary);
        font-weight: 500;
        transition: all 0.3s ease;
        border: none;
        padding: 16px 24px;
        margin: 0 4px;
        position: relative;
        z-index: 1;
        font-size: 15px;
        flex: 1;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        letter-spacing: 0.3px;
    }
    
    .stTabs [data-baseweb="tab"]:hover {
        color: var(--text-primary);
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px 8px 0 0;
    }
    
    /* Hide the default Streamlit tab highlight */
    .stTabs [data-baseweb="tab-highlight"] {
        display: none !important;
    }
    
    /* Custom active tab indicator */
    .stTabs [aria-selected="true"] {
        background: transparent !important;
        /* color: var(--primary-color) !important;  Using theme's primary blue */
        font-weight: 600;
        box-shadow: none;
    }
    
    .stTabs [aria-selected="true"]::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--primary-color); /* Using theme's primary blue */
        border-radius: 3px 3px 0 0;
    }
    
    /* Enhanced sidebar */
    .css-1d391kg, [data-testid="stSidebar"] {
        background: linear-gradient(180deg, rgba(30, 41, 59, 0.95), rgba(15, 23, 42, 0.95));
        backdrop-filter: blur(10px);
        border-right: 1px solid var(--border-color);
    }
    
    .css-1d391kg h1, [data-testid="stSidebar"] h1 {
        color: var(--text-primary);
        font-weight: 700;
        letter-spacing: -0.01em;
    }
    
    /* Enhanced selectbox */
    .stSelectbox > div > div {
        background: rgba(30, 41, 59, 0.8);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        color: var(--text-primary);
        transition: all 0.3s ease;
    }
    
    .stSelectbox > div > div:hover {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
    
    /* Footer enhancement */
    .footer-text {
        text-align: center;
        color: var(--text-tertiary);
        font-size: 0.875rem;
        font-weight: 500;
        padding: 2rem 0;
        border-top: 1px solid var(--border-color);
        margin-top: 3rem;
    }
    
    /* Smooth scrolling */
    html {
        scroll-behavior: smooth;
    }
    
    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 10px;
        height: 10px;
    }
    
    ::-webkit-scrollbar-track {
        background: var(--bg-secondary);
    }
    
    ::-webkit-scrollbar-thumb {
        background: var(--bg-tertiary);
        border-radius: 5px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: var(--primary-color);
    }
    
    /* Animation for page load */
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .kpi-card, .metric-item, .chart-container {
        animation: fadeIn 0.6s ease-out;
    }
    
    /* Responsive improvements */
    @media (max-width: 768px) {
        .kpi-value {
            font-size: 2rem;
        }
        
        .metric-value {
            font-size: 1.25rem;
        }
        
        .section-header {
            font-size: 1.5rem;
        }
    }

    header[data-testid="stHeader"] {visibility: hidden;}



    /* Budget filters special styling */
    .stSelectbox:has(#period_select_widget),
    .stSelectbox:has(#week_select_widget) {
        margin-bottom: 15px;
    }
    
    .stSelectbox:has(#period_select_widget) > div > div,
    .stSelectbox:has(#week_select_widget) > div > div {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(139, 92, 246, 0.15));
        border: 2px solid rgba(59, 130, 246, 0.4);
        border-radius: 10px;
        color: var(--text-primary);
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
    }
    
    .stSelectbox:has(#period_select_widget) > div > div:hover,
    .stSelectbox:has(#week_select_widget) > div > div:hover {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(59, 130, 246, 0.3);
        transform: translateY(-1px);
    }

    /* Enhanced styling for main filter controls */
    .stSelectbox:has(#country_select_widget) > div > div,
    .stSelectbox:has(#store_select_widget) > div > div {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(139, 92, 246, 0.15));
        border: 2px solid rgba(59, 130, 246, 0.4);
        border-radius: 10px;
        color: var(--text-primary);
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
    }
    
    .stSelectbox:has(#country_select_widget) > div > div:hover,
    .stSelectbox:has(#store_select_widget) > div > div:hover {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(59, 130, 246, 0.3);
        transform: translateY(-1px);
    }
    
    /* Style the toggle switch for store filter */
    .stCheckbox:has(#store_toggle_widget) {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
        padding: 10px 15px;
        border-radius: 8px;
        border: 1px solid rgba(59, 130, 246, 0.3);
        margin: 10px 0;
    }



/* Budget specific container styling */
.st-key-budget_activity_pie_chart,
.st-key-budget_semi_circle_chart {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(51, 65, 85, 0.95));
    backdrop-filter: blur(10px);
    padding: 20px;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.st-key-budget_activity_pie_chart:hover,
.st-key-budget_semi_circle_chart:hover {
    box-shadow: var(--shadow-xl);
    border-color: rgba(59, 130, 246, 0.2);
}

/* Budget layout containers - Clean spacing */
.st-key-budget_top_part,
.st-key-budget_bottom_part {
    margin-bottom: 20px;
    padding: 0;
}

/* Controlled column spacing */
.st-key-budget_top_part > div,
.st-key-budget_bottom_part > div {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

/* Column alignment and spacing */
.st-key-budget_top_part [data-testid="column"],
.st-key-budget_bottom_part [data-testid="column"] {
    padding: 0;
    margin: 0;
}

/* Right column proper stacking */
.st-key-budget_bottom_part [data-testid="column"]:last-child {
    display: flex;
    flex-direction: column;
    gap: 0;
    align-items: stretch;
}

/* Ensure charts fill the right column width */
.st-key-budget_bottom_part .st-key-budget_activity_pie_chart,
.st-key-budget_bottom_part .st-key-budget_semi_circle_chart {
    width: 100%;
    margin-bottom: 20px;
}

/* Remove bottom margin from last chart */
.st-key-budget_bottom_part .st-key-budget_semi_circle_chart {
    margin-bottom: 0;
}

/* Align both sections properly */
.st-key-budget_top_part [data-testid="column"]:last-child,
.st-key-budget_bottom_part [data-testid="column"]:last-child {
    width: 100%;
    max-width: 400px; /* Consistent width for right column */
}

/* Left column consistency */
.st-key-budget_top_part [data-testid="column"]:first-child,
.st-key-budget_bottom_part [data-testid="column"]:first-child {
    flex: 1;
    min-width: 0; /* Allows flex shrinking */
}



/* Navigation buttons styling */
.st-key-prev_week_btn button,
.st-key-next_week_btn button,
.st-key-prev_period_btn button,
.st-key-next_period_btn button {
    background: transparent !important;
    color: white !important;
    border: 1px solid rgba(59, 130, 246, 0.4) !important;
    transition: all 0.2s ease !important;
}

.st-key-prev_week_btn button:focus:not(:active),
.st-key-next_week_btn button:focus:not(:active),
.st-key-prev_period_btn button:focus:not(:active),
.st-key-next_period_btn button:focus:not(:active) {
    outline: none !important;
    box-shadow: none !important;

}

.st-key-prev_week_btn button:hover,
.st-key-next_week_btn button:hover,
.st-key-prev_period_btn button:hover,
.st-key-next_period_btn button:hover {
    background: rgba(59, 130, 246, 0.2) !important;
    border-color: #3b82f6 !important;
}

.st-key-prev_week_btn button:disabled,
.st-key-next_week_btn button:disabled,
.st-key-prev_period_btn button:disabled,
.st-key-next_period_btn button:disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
}

</style>
"""

def get_tabs_styles():
    """
    Returns the CSS styles for the tabs.
    """
    return """
<style>
    /* Ensure proper tab content alignment */
    .stTabs [role="tabpanel"] {
        padding-top: 20px;
    }
</style>
"""
